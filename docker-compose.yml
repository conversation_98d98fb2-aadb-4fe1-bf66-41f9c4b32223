version: '3.8'
networks:
  spark-network:
    driver: bridge

services:
  # Spark Metadata-Driven Pipeline
  spark-pipeline:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: spark-pipeline
    environment:
      # Database connections
      - CONFIG_ROOT_DIR=/app/src/main/config
      - CONFIG_ENDPOINTS_DIR=/app/src/main/config/data/endpoints
      - CONFIG_JOBS_DIR=/app/src/main/config/data/jobs
      - EXTRA_JARS_FOLDER=/opt/spark/jars/extra
      # Spark configuration
      - SPARK_MASTER=local[*]
      - SPARK_DRIVER_MEMORY=2g
      - SPARK_EXECUTOR_MEMORY=2g
      # AWS/S3 configuration for LocalStack
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      - AWS_DEFAULT_REGION=us-east-1
      - S3_ENDPOINT=http://localstack:4566
      - S3_PATH_STYLE_ACCESS=true
    volumes:
      - ./src:/app/src
      - ./logs:/app/logs
      - ./data:/app/data
    ports:
      - "4040:4040"  # Spark UI
      - "4041:4041"  # Additional Spark UI
    networks:
      - spark-network
    depends_on:
      - postgres
      - mysql
      - sqlserver
      - localstack
    # Keep container running for interactive use
    tty: true
    stdin_open: true
  postgres:
    container_name: postgres
    image: postgres:16
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 123456
      POSTGRES_DB: test
    restart: always
    networks:
      - spark-network
    ports:
      - "5432:5432"

  mysql:
    image: mysql:8.0.41
    container_name: mysql
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: test
      MYSQL_USER: test
      MYSQL_PASSWORD: 123456
    ports:
      - "3306:3306"
    volumes:
      - mysql-data:/var/lib/mysql
    networks:
      - spark-network

  sqlserver:
    image: mcr.microsoft.com/mssql/server:latest
    container_name: sqlserver
    platform: linux/amd64 # Force x86 emulation
    restart: always
    environment:
      SA_PASSWORD: "123456aA"
      ACCEPT_EULA: "Y"
    ports:
      - "1433:1433"
    volumes:
      - sqlserver-data:/var/opt/mssql
    networks:
      - spark-network

  localstack:
    image: localstack/localstack:latest
    container_name: localstack
    ports:
      - "4566:4566" # main entry point for all services
    environment:
      - SERVICES=s3,lambda,dynamodb,cloudwatch,sqs,sns
      - DEBUG=1
      - DATA_DIR=/tmp/localstack/data
    volumes:
      - ./localstack:/var/lib/localstack
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - spark-network
volumes:
  mysql-data:
  sqlserver-data:
