"""
Configuration path manager for the spark-metadata-driven pipeline.

Provides centralized management of configuration paths and environment settings.
"""
import os
from pathlib import Path
from typing import Optional
from .environment import EnvironmentConfig


class ConfigManager:
    """Manages configuration paths and environment settings."""
    
    def __init__(self, config_root_dir: Optional[str] = None):
        """
        Initialize the configuration manager.
        
        Args:
            config_root_dir: Root directory for configurations. If None, uses environment variable.
        """
        self.env_config = EnvironmentConfig()
        self._config_root_dir = config_root_dir or self.env_config.config_root_dir
        self._setup_environment_variables()
    
    def _setup_environment_variables(self):
        """Set up environment variables for configuration paths."""
        # Set the main config directories
        os.environ["CONFIG_ROOT_DIR"] = self._config_root_dir
        os.environ["CONFIG_ENDPOINTS_DIR"] = self.endpoints_dir
        os.environ["CONFIG_JOBS_DIR"] = self.jobs_dir
    
    @property
    def config_root_dir(self) -> str:
        """Get the root configuration directory."""
        return self._config_root_dir
    
    @property
    def endpoints_dir(self) -> str:
        """Get the endpoints configuration directory."""
        if self._config_root_dir.startswith("s3://"):
            return f"{self._config_root_dir.rstrip('/')}/data/endpoints"
        return f"{self._config_root_dir.rstrip('/')}/data/endpoints"
    
    @property
    def jobs_dir(self) -> str:
        """Get the jobs configuration directory."""
        if self._config_root_dir.startswith("s3://"):
            return f"{self._config_root_dir.rstrip('/')}/data/jobs"
        return f"{self._config_root_dir.rstrip('/')}/data/jobs"
    
    @property
    def schemas_dir(self) -> str:
        """Get the schemas directory (local only)."""
        return str(Path(__file__).parent.parent / "schemas")
    
    def get_endpoint_path(self, endpoint_filename: str) -> str:
        """
        Get the full path to an endpoint configuration file.
        
        Args:
            endpoint_filename: Name of the endpoint file (e.g., 'mysql.endpoint.yaml')
            
        Returns:
            Full path to the endpoint file
        """
        return f"{self.endpoints_dir}/{endpoint_filename}"
    
    def get_job_path(self, job_filename: str) -> str:
        """
        Get the full path to a job configuration file.
        
        Args:
            job_filename: Name of the job file (e.g., 'job.sample.yaml')
            
        Returns:
            Full path to the job file
        """
        return f"{self.jobs_dir}/{job_filename}"
    
    def is_s3_config(self) -> bool:
        """Check if configuration is stored in S3."""
        return self._config_root_dir.startswith("s3://")
    
    def get_s3_config_paths(self) -> tuple[str, str]:
        """
        Get S3-compatible paths for jobs and endpoints.
        
        Returns:
            Tuple of (job_root_dir, endpoint_root_dir) with s3a:// prefix
        """
        if self.is_s3_config():
            # Convert s3:// to s3a:// for Spark compatibility
            base_path = self._config_root_dir.replace("s3://", "s3a://")
            job_root_dir = f"{base_path}/data/jobs"
            endpoint_root_dir = f"{base_path}/data/endpoints"
        else:
            job_root_dir = self.jobs_dir
            endpoint_root_dir = self.endpoints_dir
        
        return job_root_dir, endpoint_root_dir


# Global configuration manager instance
_config_manager: Optional[ConfigManager] = None


def get_config_manager(config_root_dir: Optional[str] = None) -> ConfigManager:
    """
    Get the global configuration manager instance.
    
    Args:
        config_root_dir: Root directory for configurations. Only used on first call.
        
    Returns:
        ConfigManager instance
    """
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager(config_root_dir)
    return _config_manager


def reset_config_manager():
    """Reset the global configuration manager. Useful for testing."""
    global _config_manager
    _config_manager = None
