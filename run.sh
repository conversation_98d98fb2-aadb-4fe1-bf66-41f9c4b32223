#!/bin/bash

# Simple script to build and run the Spark pipeline

echo "🚀 Building Spark Pipeline Docker image..."
docker build -t spark-pipeline .

echo "🔧 Starting database services..."
docker-compose up -d postgres mysql sqlserver localstack

echo "⏳ Waiting for services to be ready..."
sleep 10

echo "🏃 Running the pipeline..."
docker run --rm \
  --network spark-metadata-driven_spark-network \
  -v "$(pwd)/src:/app/src" \
  -v "$(pwd)/logs:/app/logs" \
  spark-pipeline

echo "✅ Pipeline completed!"
