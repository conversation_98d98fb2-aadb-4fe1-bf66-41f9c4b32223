# Docker Deployment Guide

This guide explains how to deploy and run the Spark Metadata-Driven Pipeline using Docker.

## 🚀 Quick Start

### Prerequisites

- Docker (version 20.10 or later)
- Docker Compose (version 2.0 or later)
- At least 8GB of available RAM
- At least 10GB of free disk space

### 1. Setup Environment

```bash
# Clone the repository
git clone <repository-url>
cd spark-metadata-driven

# Run the setup script
./docker/setup.sh
```

### 2. Build and Start Services

```bash
# Build the pipeline Docker image
./docker/build.sh

# Start all services
docker-compose up -d

# Check service status
docker-compose ps
```

### 3. Initialize Databases

```bash
# Setup databases (run once)
docker-compose exec spark-pipeline ./scripts/setup_databases.sh
```

### 4. Run Your First Job

```bash
# Run a sample job
./docker/run.sh job job.sample.yaml

# Or using docker-compose
docker-compose exec spark-pipeline python src/main/pipeline_main.py \
  --config_file job.sample.yaml \
  --config_source_type local \
  --config_root_dir /app/src/main/config \
  --app_type batch \
  --lowerbound '' \
  --upperbound '' \
  --report_date $(date +%Y-%m-%d) \
  --pipeline '' \
  --s3_type local
```

## 📁 Project Structure

```
spark-metadata-driven/
├── docker/                    # Docker-related scripts
│   ├── build.sh              # Build Docker images
│   ├── run.sh                # Run containers
│   └── setup.sh              # Environment setup
├── Dockerfile                # Main Dockerfile
├── Dockerfile.optimized      # Optimized multi-stage Dockerfile
├── docker-compose.yml        # Main compose configuration
├── docker-compose.prod.yml   # Production overrides
├── .env.example              # Environment variables template
└── src/                      # Application source code
```

## 🛠️ Available Scripts

### Build Scripts

```bash
# Build with default settings
./docker/build.sh

# Build optimized version
./docker/build.sh --optimized

# Build without cache
./docker/build.sh --no-cache

# Build with custom name and tag
./docker/build.sh --name my-pipeline --tag v1.0.0
```

### Run Scripts

```bash
# Show help
./docker/run.sh help

# Start interactive shell
./docker/run.sh bash

# Run tests
./docker/run.sh test

# Run specific job
./docker/run.sh job job.sample.yaml

# Run in detached mode
./docker/run.sh --detach job job.sample.yaml
```

## 🔧 Configuration

### Environment Variables

Copy `.env.example` to `.env` and customize:

```bash
cp .env.example .env
# Edit .env with your preferred settings
```

Key configuration options:

- `SPARK_MASTER`: Spark master URL (default: local[*])
- `SPARK_DRIVER_MEMORY`: Driver memory allocation
- `SPARK_EXECUTOR_MEMORY`: Executor memory allocation
- Database connection settings
- AWS/S3 configuration

### Database Configuration

The pipeline supports multiple databases:

- **PostgreSQL**: localhost:5432 (postgres/123456)
- **MySQL**: localhost:3306 (test/123456)
- **SQL Server**: localhost:1433 (sa/123456aA)

### S3 Configuration

For local development, LocalStack provides S3 compatibility:
- **LocalStack S3**: http://localhost:4566

For production, configure real AWS credentials in `.env`.

## 🏗️ Development Setup

### Development Mode

```bash
# Copy development override
cp docker-compose.override.yml.example docker-compose.override.yml

# Start in development mode
docker-compose up -d

# Access development shell
docker-compose exec spark-pipeline bash
```

### Code Changes

In development mode, source code is mounted as a volume, so changes are reflected immediately without rebuilding.

### Debugging

```bash
# View logs
docker-compose logs spark-pipeline

# Follow logs
docker-compose logs -f spark-pipeline

# Access Spark UI
open http://localhost:4040
```

## 🚀 Production Deployment

### Production Build

```bash
# Build optimized production image
./docker/build.sh --optimized --tag production

# Start production services
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### Production Considerations

1. **Resource Limits**: Configure appropriate memory and CPU limits
2. **Persistent Storage**: Use named volumes for database data
3. **Security**: Use secrets management for sensitive data
4. **Monitoring**: Add monitoring and logging solutions
5. **Backup**: Implement database backup strategies

## 🔍 Troubleshooting

### Common Issues

1. **Out of Memory**
   ```bash
   # Increase Docker memory limit or reduce Spark memory settings
   # Edit .env file:
   SPARK_DRIVER_MEMORY=1g
   SPARK_EXECUTOR_MEMORY=1g
   ```

2. **Port Conflicts**
   ```bash
   # Check for port conflicts
   docker-compose ps
   netstat -tulpn | grep :4040
   ```

3. **Database Connection Issues**
   ```bash
   # Check database containers
   docker-compose logs postgres
   docker-compose logs mysql
   docker-compose logs sqlserver
   ```

4. **JAR Dependencies**
   ```bash
   # Rebuild with fresh JARs
   ./docker/build.sh --no-cache
   ```

### Logs and Debugging

```bash
# View all service logs
docker-compose logs

# View specific service logs
docker-compose logs spark-pipeline

# Access container shell
docker-compose exec spark-pipeline bash

# Check Spark application logs
docker-compose exec spark-pipeline ls -la /app/logs/
```

## 📊 Monitoring

### Spark UI

Access the Spark UI at http://localhost:4040 to monitor:
- Job progress
- Stage details
- Executor information
- SQL queries

### Database Access

Connect to databases using your preferred client:

```bash
# PostgreSQL
psql -h localhost -p 5432 -U postgres -d test

# MySQL
mysql -h localhost -P 3306 -u test -p test

# SQL Server
sqlcmd -S localhost,1433 -U sa -P 123456aA
```

## 🧪 Testing

```bash
# Run all tests
./docker/run.sh test

# Run specific test file
docker-compose exec spark-pipeline python -m pytest src/test/config/ -v

# Run with coverage
docker-compose exec spark-pipeline python -m pytest --cov=src/main src/test/
```

## 🔄 Updates and Maintenance

### Updating the Pipeline

```bash
# Pull latest changes
git pull origin main

# Rebuild image
./docker/build.sh

# Restart services
docker-compose down
docker-compose up -d
```

### Cleaning Up

```bash
# Stop all services
docker-compose down

# Remove volumes (WARNING: This deletes all data)
docker-compose down -v

# Clean up Docker system
docker system prune -a
```

## 📞 Support

For issues and questions:
1. Check the troubleshooting section above
2. Review Docker and application logs
3. Check the main README.md for application-specific help
4. Create an issue in the project repository
