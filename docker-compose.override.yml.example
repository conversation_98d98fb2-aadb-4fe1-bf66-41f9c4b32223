# Docker Compose Override Example
# Copy this file to docker-compose.override.yml for local development customizations

version: '3.8'

services:
  spark-pipeline:
    # Override for development
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - DEV_MODE=true
    environment:
      # Development-specific environment variables
      - LOG_LEVEL=DEBUG
      - DEV_MODE=true
    volumes:
      # Additional volume mounts for development
      - ./src:/app/src:rw
      - ./logs:/app/logs:rw
      - ./data:/app/data:rw
      - ./docker:/app/docker:ro
    # Override resource limits for development
    deploy:
      resources:
        limits:
          memory: 4g
        reservations:
          memory: 2g

  postgres:
    # Development database with persistent data
    environment:
      - POSTGRES_DB=dev_test
    volumes:
      - postgres-dev-data:/var/lib/postgresql/data
      - ./scripts/sql/postgres:/docker-entrypoint-initdb.d:ro

  mysql:
    # Development database with persistent data
    environment:
      - MYSQL_DATABASE=dev_test
    volumes:
      - mysql-dev-data:/var/lib/mysql
      - ./scripts/sql/mysql:/docker-entrypoint-initdb.d:ro

  sqlserver:
    # Development database with persistent data
    volumes:
      - sqlserver-dev-data:/var/opt/mssql
      - ./scripts/sql/sqlserver:/docker-entrypoint-initdb.d:ro

  localstack:
    # Development S3 with persistent data
    environment:
      - SERVICES=s3,lambda,dynamodb,cloudwatch,sqs,sns,iam
      - DEBUG=1
      - PERSISTENCE=1
    volumes:
      - ./localstack:/var/lib/localstack:rw
      - ./scripts/localstack:/docker-entrypoint-initaws.d:ro

volumes:
  postgres-dev-data:
  mysql-dev-data:
  sqlserver-dev-data:
