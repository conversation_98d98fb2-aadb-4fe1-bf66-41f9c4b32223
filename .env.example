# Spark Metadata-Driven Pipeline Environment Configuration
# Copy this file to .env and modify as needed

# ===========================================
# Spark Configuration
# ===========================================
SPARK_MASTER=local[*]
SPARK_DRIVER_MEMORY=2g
SPARK_EXECUTOR_MEMORY=2g
SPARK_DRIVER_MAX_RESULT_SIZE=1g
SPARK_SQL_ADAPTIVE_ENABLED=true
SPARK_SQL_ADAPTIVE_COALESCE_PARTITIONS_ENABLED=true

# ===========================================
# Database Configuration
# ===========================================

# PostgreSQL
POSTGRES_USER=postgres
POSTGRES_PASSWORD=123456
POSTGRES_DB=test
POSTGRES_HOST=postgres
POSTGRES_PORT=5432

# MySQL
MYSQL_ROOT_PASSWORD=root
MYSQL_DATABASE=test
MYSQL_USER=test
MYSQL_PASSWORD=123456
MYSQL_HOST=mysql
MYSQL_PORT=3306

# SQL Server
MSSQL_SA_PASSWORD=123456aA
MSSQL_HOST=sqlserver
MSSQL_PORT=1433

# ===========================================
# AWS/S3 Configuration
# ===========================================

# For LocalStack (local development)
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test
AWS_DEFAULT_REGION=us-east-1
S3_ENDPOINT=http://localstack:4566
S3_PATH_STYLE_ACCESS=true

# For AWS Production (uncomment and configure)
# AWS_ACCESS_KEY_ID=your_access_key
# AWS_SECRET_ACCESS_KEY=your_secret_key
# AWS_DEFAULT_REGION=us-east-1
# S3_ENDPOINT=
# S3_PATH_STYLE_ACCESS=false

# ===========================================
# Pipeline Configuration
# ===========================================
CONFIG_ROOT_DIR=/app/src/main/config
CONFIG_ENDPOINTS_DIR=/app/src/main/config/data/endpoints
CONFIG_JOBS_DIR=/app/src/main/config/data/jobs
EXTRA_JARS_FOLDER=/opt/spark/jars/extra

# Application Settings
APP_NAME_PREFIX=spark-metadata-pipeline
LOG_LEVEL=INFO

# ===========================================
# Docker Configuration
# ===========================================
COMPOSE_PROJECT_NAME=spark-metadata-driven

# Container resource limits
SPARK_CONTAINER_MEMORY=4g
POSTGRES_CONTAINER_MEMORY=512m
MYSQL_CONTAINER_MEMORY=512m
MSSQL_CONTAINER_MEMORY=2g

# ===========================================
# Development Configuration
# ===========================================

# Set to true for development mode
DEV_MODE=true

# Mount source code for development
MOUNT_SOURCE_CODE=true

# Enable debug logging
DEBUG=false
