# Multi-stage Dockerfile for optimized production builds
# Stage 1: Build stage
FROM openjdk:11-jdk-slim as builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    wget \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Download Spark
ARG SPARK_VERSION=3.5.0
ARG HADOOP_VERSION=3
WORKDIR /tmp
RUN wget -q "https://archive.apache.org/dist/spark/spark-${SPARK_VERSION}/spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION}.tgz" \
    && tar xzf "spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION}.tgz" \
    && mv "spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION}" /opt/spark

# Download JARs
COPY scripts/download_jars.sh /tmp/
RUN chmod +x /tmp/download_jars.sh && \
    mkdir -p /opt/spark/jars/extra && \
    cd /opt/spark/jars/extra && \
    /tmp/download_jars.sh

# Stage 2: Runtime stage
FROM openjdk:11-jre-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV JAVA_HOME=/usr/local/openjdk-11
ENV SPARK_HOME=/opt/spark
ENV PATH=$PATH:$SPARK_HOME/bin:$SPARK_HOME/sbin
ENV PYTHONPATH=$SPARK_HOME/python:$SPARK_HOME/python/lib/py4j-*.zip

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-dev \
    procps \
    net-tools \
    && rm -rf /var/lib/apt/lists/*

# Create symbolic link for python
RUN ln -sf /usr/bin/python3 /usr/bin/python

# Copy Spark from builder stage
COPY --from=builder /opt/spark /opt/spark

# Create application directory and user
RUN useradd -m -u 1000 sparkuser
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip3 install --no-cache-dir -r requirements.txt

# Copy application code
COPY --chown=sparkuser:sparkuser src/ ./src/
COPY --chown=sparkuser:sparkuser scripts/ ./scripts/
COPY --chown=sparkuser:sparkuser pytest.ini .

# Create necessary directories
RUN mkdir -p /app/logs /app/data && \
    chown -R sparkuser:sparkuser /app /opt/spark

# Make scripts executable
RUN chmod +x scripts/*.sh

# Set default configuration paths
ENV CONFIG_ROOT_DIR=/app/src/main/config
ENV CONFIG_ENDPOINTS_DIR=/app/src/main/config/data/endpoints
ENV CONFIG_JOBS_DIR=/app/src/main/config/data/jobs
ENV EXTRA_JARS_FOLDER=/opt/spark/jars/extra

# Switch to non-root user
USER sparkuser

# Expose port for Spark UI
EXPOSE 4040

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python -c "import pyspark; print('Spark is available')" || exit 1

# Default command
CMD ["python", "src/main/pipeline_main.py", "--help"]
