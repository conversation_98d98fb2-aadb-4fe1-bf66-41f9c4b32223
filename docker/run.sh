#!/bin/bash

# Run script for Spark Metadata-Driven Pipeline
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
IMAGE_NAME="spark-metadata-pipeline"
TAG="latest"
CONTAINER_NAME="spark-pipeline-run"
COMMAND=""
INTERACTIVE=true
REMOVE_AFTER=true

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_command() {
    echo -e "${BLUE}[CMD]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS] [COMMAND]"
    echo ""
    echo "Options:"
    echo "  -n, --name NAME       Docker image name (default: spark-metadata-pipeline)"
    echo "  -t, --tag TAG         Docker image tag (default: latest)"
    echo "  -c, --container NAME  Container name (default: spark-pipeline-run)"
    echo "  -d, --detach          Run in detached mode"
    echo "  --keep                Keep container after exit (don't auto-remove)"
    echo "  -h, --help            Show this help message"
    echo ""
    echo "Commands:"
    echo "  bash                  Start interactive bash shell"
    echo "  test                  Run tests"
    echo "  job JOBFILE           Run specific job file"
    echo "  help                  Show pipeline help"
    echo ""
    echo "Examples:"
    echo "  $0                    # Run with default command (help)"
    echo "  $0 bash               # Start interactive shell"
    echo "  $0 test               # Run tests"
    echo "  $0 job job.sample.yaml # Run specific job"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -n|--name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -c|--container)
            CONTAINER_NAME="$2"
            shift 2
            ;;
        -d|--detach)
            INTERACTIVE=false
            shift
            ;;
        --keep)
            REMOVE_AFTER=false
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        bash)
            COMMAND="/bin/bash"
            shift
            ;;
        test)
            COMMAND="python -m pytest src/test/ -v"
            shift
            ;;
        job)
            if [[ -z "$2" ]]; then
                print_error "Job file name required after 'job' command"
                exit 1
            fi
            COMMAND="python src/main/pipeline_main.py --config_file $2 --config_source_type local --config_root_dir /app/src/main/config --app_type batch --lowerbound '' --upperbound '' --report_date $(date +%Y-%m-%d) --pipeline '' --s3_type local"
            shift 2
            ;;
        help)
            COMMAND="python src/main/pipeline_main.py --help"
            shift
            ;;
        *)
            # Treat as custom command
            COMMAND="$*"
            break
            ;;
    esac
done

# Set default command if none provided
if [[ -z "$COMMAND" ]]; then
    COMMAND="python src/main/pipeline_main.py --help"
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if image exists
if ! docker image inspect "$IMAGE_NAME:$TAG" > /dev/null 2>&1; then
    print_error "Docker image '$IMAGE_NAME:$TAG' not found!"
    print_status "Build the image first with: ./docker/build.sh"
    exit 1
fi

# Prepare Docker run options
DOCKER_OPTS=""
if [[ "$INTERACTIVE" == true ]]; then
    DOCKER_OPTS="$DOCKER_OPTS -it"
fi

if [[ "$REMOVE_AFTER" == true ]]; then
    DOCKER_OPTS="$DOCKER_OPTS --rm"
fi

# Create necessary directories
mkdir -p logs data

print_status "Running Spark Metadata-Driven Pipeline..."
print_status "Image: $IMAGE_NAME:$TAG"
print_status "Container: $CONTAINER_NAME"
print_command "$COMMAND"

# Run the container
docker run $DOCKER_OPTS \
    --name "$CONTAINER_NAME" \
    --network spark-metadata-driven_spark-network \
    -v "$(pwd)/src:/app/src" \
    -v "$(pwd)/logs:/app/logs" \
    -v "$(pwd)/data:/app/data" \
    -p 4040:4040 \
    -p 4041:4041 \
    -e CONFIG_ROOT_DIR=/app/src/main/config \
    -e CONFIG_ENDPOINTS_DIR=/app/src/main/config/data/endpoints \
    -e CONFIG_JOBS_DIR=/app/src/main/config/data/jobs \
    -e EXTRA_JARS_FOLDER=/opt/spark/jars/extra \
    "$IMAGE_NAME:$TAG" \
    $COMMAND
