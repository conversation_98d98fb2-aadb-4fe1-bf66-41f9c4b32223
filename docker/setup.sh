#!/bin/bash

# Setup script for Spark Metadata-Driven Pipeline Docker environment
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check Docker installation
check_docker() {
    print_header "Checking Docker Installation"
    
    if ! command_exists docker; then
        print_error "Docker is not installed!"
        print_status "Please install Docker from: https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running!"
        print_status "Please start Docker and try again."
        exit 1
    fi
    
    DOCKER_VERSION=$(docker --version)
    print_status "✅ Docker is installed and running: $DOCKER_VERSION"
}

# Function to check Docker Compose installation
check_docker_compose() {
    print_header "Checking Docker Compose Installation"
    
    if ! command_exists docker-compose && ! docker compose version > /dev/null 2>&1; then
        print_error "Docker Compose is not installed!"
        print_status "Please install Docker Compose from: https://docs.docker.com/compose/install/"
        exit 1
    fi
    
    if command_exists docker-compose; then
        COMPOSE_VERSION=$(docker-compose --version)
        print_status "✅ Docker Compose is installed: $COMPOSE_VERSION"
    else
        COMPOSE_VERSION=$(docker compose version)
        print_status "✅ Docker Compose (plugin) is installed: $COMPOSE_VERSION"
    fi
}

# Function to create necessary directories
create_directories() {
    print_header "Creating Necessary Directories"
    
    directories=("logs" "data" "localstack")
    
    for dir in "${directories[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            print_status "Created directory: $dir"
        else
            print_status "Directory already exists: $dir"
        fi
    done
}

# Function to make scripts executable
make_scripts_executable() {
    print_header "Making Scripts Executable"
    
    scripts=("docker/build.sh" "docker/run.sh" "docker/setup.sh" "scripts/setup_databases.sh" "scripts/run_pipeline.sh")
    
    for script in "${scripts[@]}"; do
        if [[ -f "$script" ]]; then
            chmod +x "$script"
            print_status "Made executable: $script"
        else
            print_warning "Script not found: $script"
        fi
    done
}

# Function to create .env file
create_env_file() {
    print_header "Creating Environment Configuration"
    
    if [[ ! -f ".env" ]]; then
        cat > .env << 'EOF'
# Spark Configuration
SPARK_MASTER=local[*]
SPARK_DRIVER_MEMORY=2g
SPARK_EXECUTOR_MEMORY=2g

# Database Configuration
POSTGRES_USER=postgres
POSTGRES_PASSWORD=123456
POSTGRES_DB=test

MYSQL_ROOT_PASSWORD=root
MYSQL_DATABASE=test
MYSQL_USER=test
MYSQL_PASSWORD=123456

MSSQL_SA_PASSWORD=123456aA

# AWS/S3 Configuration (for LocalStack)
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test
AWS_DEFAULT_REGION=us-east-1
S3_ENDPOINT=http://localstack:4566
S3_PATH_STYLE_ACCESS=true

# Pipeline Configuration
CONFIG_ROOT_DIR=/app/src/main/config
CONFIG_ENDPOINTS_DIR=/app/src/main/config/data/endpoints
CONFIG_JOBS_DIR=/app/src/main/config/data/jobs
EXTRA_JARS_FOLDER=/opt/spark/jars/extra
EOF
        print_status "Created .env file with default configuration"
    else
        print_status ".env file already exists"
    fi
}

# Function to pull required Docker images
pull_images() {
    print_header "Pulling Required Docker Images"
    
    images=("postgres:16" "mysql:8.0.41" "mcr.microsoft.com/mssql/server:latest" "localstack/localstack:latest")
    
    for image in "${images[@]}"; do
        print_status "Pulling $image..."
        docker pull "$image"
    done
}

# Function to show next steps
show_next_steps() {
    print_header "Setup Complete! Next Steps"
    
    echo ""
    print_status "1. Build the pipeline Docker image:"
    echo "   ./docker/build.sh"
    echo ""
    print_status "2. Start all services:"
    echo "   docker-compose up -d"
    echo ""
    print_status "3. Setup databases (run once):"
    echo "   docker-compose exec spark-pipeline ./scripts/setup_databases.sh"
    echo ""
    print_status "4. Run a sample job:"
    echo "   ./docker/run.sh job job.sample.yaml"
    echo ""
    print_status "5. Access services:"
    echo "   - Spark UI: http://localhost:4040"
    echo "   - PostgreSQL: localhost:5432"
    echo "   - MySQL: localhost:3306"
    echo "   - SQL Server: localhost:1433"
    echo "   - LocalStack: http://localhost:4566"
    echo ""
    print_status "For more help:"
    echo "   ./docker/run.sh help"
}

# Main execution
main() {
    print_header "Spark Metadata-Driven Pipeline Docker Setup"
    
    check_docker
    check_docker_compose
    create_directories
    make_scripts_executable
    create_env_file
    pull_images
    show_next_steps
    
    print_status "🎉 Setup completed successfully!"
}

# Run main function
main "$@"
