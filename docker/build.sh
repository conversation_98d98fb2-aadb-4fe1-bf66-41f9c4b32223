#!/bin/bash

# Build script for Spark Metadata-Driven Pipeline Docker images
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default values
IMAGE_NAME="spark-metadata-pipeline"
TAG="latest"
DOCKERFILE="Dockerfile"
BUILD_ARGS=""

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -n, --name NAME       Docker image name (default: spark-metadata-pipeline)"
    echo "  -t, --tag TAG         Docker image tag (default: latest)"
    echo "  -f, --file FILE       Dockerfile to use (default: Dockerfile)"
    echo "  -o, --optimized       Use optimized multi-stage Dockerfile"
    echo "  --no-cache            Build without using cache"
    echo "  -h, --help            Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                    # Build with defaults"
    echo "  $0 -o                 # Build optimized version"
    echo "  $0 -t dev --no-cache  # Build dev version without cache"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -n|--name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -f|--file)
            DOCKERFILE="$2"
            shift 2
            ;;
        -o|--optimized)
            DOCKERFILE="Dockerfile.optimized"
            shift
            ;;
        --no-cache)
            BUILD_ARGS="--no-cache"
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Dockerfile exists
if [[ ! -f "$DOCKERFILE" ]]; then
    print_error "Dockerfile '$DOCKERFILE' not found!"
    exit 1
fi

print_status "Building Docker image..."
print_status "Image name: $IMAGE_NAME:$TAG"
print_status "Dockerfile: $DOCKERFILE"

# Build the Docker image
print_status "Starting build process..."
if docker build $BUILD_ARGS -f "$DOCKERFILE" -t "$IMAGE_NAME:$TAG" .; then
    print_status "✅ Build completed successfully!"
    print_status "Image: $IMAGE_NAME:$TAG"
    
    # Show image size
    SIZE=$(docker images "$IMAGE_NAME:$TAG" --format "table {{.Size}}" | tail -n 1)
    print_status "Image size: $SIZE"
    
    echo ""
    print_status "To run the container:"
    echo "  docker run -it --rm $IMAGE_NAME:$TAG"
    echo ""
    print_status "To run with docker-compose:"
    echo "  docker-compose up spark-pipeline"
    
else
    print_error "❌ Build failed!"
    exit 1
fi
