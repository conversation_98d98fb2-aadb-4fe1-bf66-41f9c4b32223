# Production Docker Compose Configuration
# Use with: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up

version: '3.8'

services:
  spark-pipeline:
    build:
      context: .
      dockerfile: Dockerfile.optimized
    environment:
      # Production environment variables
      - LOG_LEVEL=INFO
      - DEV_MODE=false
      # Use production S3 instead of LocalStack
      - S3_ENDPOINT=
      - S3_PATH_STYLE_ACCESS=false
    volumes:
      # Read-only source code in production
      - ./src:/app/src:ro
      - ./logs:/app/logs:rw
      - ./data:/app/data:rw
    # Production resource limits
    deploy:
      resources:
        limits:
          memory: 8g
          cpus: '4'
        reservations:
          memory: 4g
          cpus: '2'
    restart: unless-stopped
    # Remove interactive flags for production
    tty: false
    stdin_open: false

  postgres:
    # Production database configuration
    environment:
      - POSTGRES_DB=production
    volumes:
      - postgres-prod-data:/var/lib/postgresql/data
    deploy:
      resources:
        limits:
          memory: 1g
        reservations:
          memory: 512m
    restart: unless-stopped

  mysql:
    # Production database configuration
    environment:
      - MYSQL_DATABASE=production
    volumes:
      - mysql-prod-data:/var/lib/mysql
    deploy:
      resources:
        limits:
          memory: 1g
        reservations:
          memory: 512m
    restart: unless-stopped

  sqlserver:
    # Production database configuration
    volumes:
      - sqlserver-prod-data:/var/opt/mssql
    deploy:
      resources:
        limits:
          memory: 4g
        reservations:
          memory: 2g
    restart: unless-stopped

  # Remove LocalStack in production - use real AWS services
  # localstack:
  #   deploy:
  #     replicas: 0

volumes:
  postgres-prod-data:
  mysql-prod-data:
  sqlserver-prod-data:
