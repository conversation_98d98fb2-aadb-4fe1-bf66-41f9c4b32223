#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  Spark Pipeline - New Machine Setup  ${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# Step 1: Check prerequisites
echo -e "${YELLOW}Step 1: Checking prerequisites...${NC}"
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker is not installed!${NC}"
    echo "Please install Docker from: https://docs.docker.com/get-docker/"
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null 2>&1; then
    echo -e "${RED}❌ Docker Compose is not installed!${NC}"
    echo "Please install Docker Compose from: https://docs.docker.com/compose/install/"
    exit 1
fi

echo -e "${GREEN}✅ Docker and Docker Compose are installed${NC}"
echo ""

# Step 2: Create directories
echo -e "${YELLOW}Step 2: Creating necessary directories...${NC}"
mkdir -p logs
mkdir -p localstack
echo -e "${GREEN}✅ Directories created${NC}"
echo ""

# Step 3: Start databases
echo -e "${YELLOW}Step 3: Starting database services...${NC}"
echo "This will start PostgreSQL, MySQL, SQL Server, and LocalStack"
docker-compose up -d

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Database services started${NC}"
else
    echo -e "${RED}❌ Failed to start database services${NC}"
    exit 1
fi
echo ""

# Step 4: Wait for services
echo -e "${YELLOW}Step 4: Waiting for services to be ready...${NC}"
echo "Waiting 30 seconds for databases to initialize..."
sleep 30
echo -e "${GREEN}✅ Services should be ready${NC}"
echo ""

# Step 5: Build Docker image
echo -e "${YELLOW}Step 5: Building Spark pipeline Docker image...${NC}"
echo "This will download Spark, install Python dependencies, and prepare everything"
docker build -t spark-pipeline .

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Docker image built successfully${NC}"
else
    echo -e "${RED}❌ Failed to build Docker image${NC}"
    exit 1
fi
echo ""

# Step 6: Setup databases
echo -e "${YELLOW}Step 6: Setting up databases with sample data...${NC}"
./scripts/setup_databases.sh

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Databases setup completed${NC}"
else
    echo -e "${YELLOW}⚠️  Database setup had some issues, but continuing...${NC}"
fi
echo ""

# Step 7: Run the pipeline
echo -e "${YELLOW}Step 7: Running your pipeline...${NC}"
echo "Running command:"
echo "python3 src/main/pipeline_main.py --config_file job.iceberg.sample.yaml --config_source_type local --config_root_dir src/main/config --app_type batch --s3_type localstack --s3_url http://localhost:4566 --extra_jars_folder src/main/resources/jars"
echo ""

docker run --rm \
  --network spark-metadata-driven_spark-network \
  -v "$(pwd)/src:/app/src" \
  -v "$(pwd)/logs:/app/logs" \
  spark-pipeline

if [ $? -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 Pipeline completed successfully!${NC}"
else
    echo ""
    echo -e "${RED}❌ Pipeline failed${NC}"
    echo "Check the logs above for error details"
    exit 1
fi

echo ""
echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}           Setup Complete!              ${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""
echo -e "${GREEN}Your pipeline is now ready to use!${NC}"
echo ""
echo "To run again:"
echo "  docker run --rm --network spark-metadata-driven_spark-network -v \"\$(pwd)/src:/app/src\" spark-pipeline"
echo ""
echo "To stop services:"
echo "  docker-compose down"
echo ""
echo "To view logs:"
echo "  ls -la logs/"
