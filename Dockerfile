# Simple Dockerfile for Spark Pipeline
FROM openjdk:11-jdk-slim

# Install Python and basic tools
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    wget \
    curl \
    gnupg \
    gpg \
    unixodbc \
    unixodbc-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Microsoft ODBC Driver for SQL Server
RUN curl -fsSL https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor -o /usr/share/keyrings/microsoft-prod.gpg \
    && curl -fsSL https://packages.microsoft.com/config/debian/11/prod.list > /etc/apt/sources.list.d/mssql-release.list \
    && echo "deb [arch=amd64,arm64,armhf signed-by=/usr/share/keyrings/microsoft-prod.gpg] https://packages.microsoft.com/debian/11/prod bullseye main" > /etc/apt/sources.list.d/mssql-release.list \
    && apt-get update \
    && ACCEPT_EULA=Y apt-get install -y msodbcsql18 \
    && rm -rf /var/lib/apt/lists/*

# Create symbolic link for python
RUN ln -sf /usr/bin/python3 /usr/bin/python

# Download and install Spark
RUN wget -q "https://archive.apache.org/dist/spark/spark-3.5.0/spark-3.5.0-bin-hadoop3.tgz" \
    && tar xzf "spark-3.5.0-bin-hadoop3.tgz" -C /opt/ \
    && mv "/opt/spark-3.5.0-bin-hadoop3" "/opt/spark" \
    && rm "spark-3.5.0-bin-hadoop3.tgz"

# Set environment variables
ENV JAVA_HOME=/usr/local/openjdk-11
ENV SPARK_HOME=/opt/spark
ENV PATH=$PATH:$SPARK_HOME/bin
ENV PYTHONPATH=$SPARK_HOME/python:$SPARK_HOME/python/lib/py4j-*.zip

# Set working directory
WORKDIR /app

# Copy and install Python dependencies
COPY requirements.txt .
RUN pip3 install --no-cache-dir -r requirements.txt

# Copy application code
COPY src/ ./src/
COPY scripts/ ./scripts/

# Download JARs
RUN chmod +x scripts/download_jars.sh && ./scripts/download_jars.sh

# JARs are already downloaded to src/main/resources/jars by the download script

# Default command - your exact command
CMD ["python3", "src/main/pipeline_main.py", \
    "--config_file", "job.iceberg.sample.yaml", \
    "--config_source_type", "local", \
    "--config_root_dir", "src/main/config", \
    "--app_type", "batch", \
    "--s3_type", "localstack", \
    "--s3_url", "http://localstack:4566", \
    "--extra_jars_folder", "src/main/resources/jars"]
