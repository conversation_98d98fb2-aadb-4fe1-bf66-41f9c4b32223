# Use official Python image with Java support for Spark
FROM openjdk:11-jdk-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV JAVA_HOME=/usr/local/openjdk-11
ENV SPARK_HOME=/opt/spark
ENV PATH=$PATH:$SPARK_HOME/bin:$SPARK_HOME/sbin
ENV PYTHONPATH=$SPARK_HOME/python:$SPARK_HOME/python/lib/py4j-*.zip

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-dev \
    wget \
    curl \
    procps \
    net-tools \
    && rm -rf /var/lib/apt/lists/*

# Create symbolic link for python
RUN ln -sf /usr/bin/python3 /usr/bin/python

# Download and install Spark
ARG SPARK_VERSION=3.5.0
ARG HADOOP_VERSION=3
RUN wget -q "https://archive.apache.org/dist/spark/spark-${SPARK_VERSION}/spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION}.tgz" \
    && tar xzf "spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION}.tgz" -C /opt/ \
    && mv "/opt/spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION}" "$SPARK_HOME" \
    && rm "spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION}.tgz"

# Create application directory
WORKDIR /app

# Copy requirements first for better Docker layer caching
COPY requirements.txt .

# Install Python dependencies
RUN pip3 install --no-cache-dir -r requirements.txt

# Create directories for JARs and configurations
RUN mkdir -p /opt/spark/jars/extra \
    && mkdir -p /app/logs \
    && mkdir -p /app/data

# Copy application code
COPY src/ ./src/
COPY scripts/ ./scripts/
COPY pytest.ini .

# Make scripts executable
RUN chmod +x scripts/*.sh

# Download required JARs
RUN ./scripts/download_jars.sh

# Set default configuration paths for containerized environment
ENV CONFIG_ROOT_DIR=/app/src/main/config
ENV CONFIG_ENDPOINTS_DIR=/app/src/main/config/data/endpoints
ENV CONFIG_JOBS_DIR=/app/src/main/config/data/jobs
ENV EXTRA_JARS_FOLDER=/opt/spark/jars/extra

# Create a non-root user for security
RUN useradd -m -u 1000 sparkuser && chown -R sparkuser:sparkuser /app /opt/spark
USER sparkuser

# Expose port for Spark UI
EXPOSE 4040

# Default command
CMD ["python", "src/main/pipeline_main.py", "--help"]
